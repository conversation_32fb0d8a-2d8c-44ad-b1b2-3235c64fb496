<?php
// Enable all error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);

echo "<h2>Multi-Hospital System Diagnostic</h2>";
echo "PHP Version: " . phpversion() . "<br>";
echo "Current Time: " . date('Y-m-d H:i:s') . "<br><br>";

// Test 1: Basic PHP functionality
echo "<h3>1. Basic PHP Test</h3>";
echo "✅ PHP is working<br><br>";

// Test 2: Database connection
echo "<h3>2. Database Connection Test</h3>";
try {
    $pdo = new PDO('mysql:host=localhost;dbname=database_tables', 'root', '');
    echo "✅ Database connection successful<br>";
    
    // Test users table
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM users");
    $result = $stmt->fetch();
    echo "✅ Users table accessible: " . $result['count'] . " records<br>";
    
} catch(PDOException $e) {
    echo "❌ Database error: " . $e->getMessage() . "<br>";
}
echo "<br>";

// Test 3: File permissions and paths
echo "<h3>3. File System Test</h3>";
echo "Current directory: " . getcwd() . "<br>";
echo "Index.php exists: " . (file_exists('index.php') ? '✅ YES' : '❌ NO') . "<br>";
echo "Application folder: " . (is_dir('application') ? '✅ YES' : '❌ NO') . "<br>";
echo "System folder: " . (is_dir('system') ? '✅ YES' : '❌ NO') . "<br>";
echo "Application writable: " . (is_writable('application') ? '✅ YES' : '❌ NO') . "<br>";
echo "<br>";

// Test 4: CodeIgniter constants and bootstrap
echo "<h3>4. CodeIgniter Bootstrap Test</h3>";
try {
    // Set up environment
    define('ENVIRONMENT', 'development');
    
    // Set up paths
    $system_path = 'system';
    $application_folder = 'application';
    
    if (realpath($system_path) !== FALSE) {
        $system_path = realpath($system_path).'/';
    }
    $system_path = rtrim($system_path, '/').'/';
    
    if (!is_dir($system_path)) {
        throw new Exception("System folder not found");
    }
    
    define('SELF', pathinfo(__FILE__, PATHINFO_BASENAME));
    define('EXT', '.php');
    define('BASEPATH', str_replace("\\", "/", $system_path));
    define('FCPATH', str_replace(SELF, '', __FILE__));
    define('SYSDIR', trim(strrchr(trim(BASEPATH, '/'), '/'), '/'));
    
    if (is_dir($application_folder)) {
        define('APPPATH', $application_folder.'/');
    } else {
        if (!is_dir(BASEPATH.$application_folder.'/')) {
            throw new Exception("Application folder not found");
        } else {
            define('APPPATH', BASEPATH.$application_folder.'/');
        }
    }
    
    echo "✅ Constants defined successfully<br>";
    echo "BASEPATH: " . BASEPATH . "<br>";
    echo "APPPATH: " . APPPATH . "<br>";
    
    // Test loading common functions
    if (file_exists(BASEPATH.'core/Common.php')) {
        require_once BASEPATH.'core/Common.php';
        echo "✅ Common.php loaded<br>";
    } else {
        throw new Exception("Common.php not found");
    }
    
    // Test config loading
    if (file_exists(APPPATH.'config/config.php')) {
        include APPPATH.'config/config.php';
        echo "✅ Config.php loaded<br>";
        echo "Base URL configured: " . (isset($config['base_url']) ? $config['base_url'] : 'NOT SET') . "<br>";
    } else {
        throw new Exception("Config.php not found");
    }
    
    // Test hooks config
    if (file_exists(APPPATH.'config/hooks.php')) {
        include APPPATH.'config/hooks.php';
        echo "✅ Hooks.php loaded<br>";
        echo "Hooks enabled: " . (isset($config['enable_hooks']) && $config['enable_hooks'] ? 'YES' : 'NO') . "<br>";
        echo "Pre-controller hook defined: " . (isset($hook['pre_controller']) ? 'YES' : 'NO') . "<br>";
    }
    
} catch(Exception $e) {
    echo "❌ Bootstrap error: " . $e->getMessage() . "<br>";
    echo "File: " . $e->getFile() . " Line: " . $e->getLine() . "<br>";
} catch(Error $e) {
    echo "❌ Fatal Error: " . $e->getMessage() . "<br>";
    echo "File: " . $e->getFile() . " Line: " . $e->getLine() . "<br>";
}
echo "<br>";

// Test 5: Try to load CodeIgniter core
echo "<h3>5. CodeIgniter Core Load Test</h3>";
try {
    ob_start();
    
    // Capture any output from CodeIgniter
    if (defined('BASEPATH')) {
        echo "Attempting to load CodeIgniter core...<br>";
        require_once BASEPATH.'core/CodeIgniter.php';
    } else {
        echo "❌ BASEPATH not defined<br>";
    }
    
    $output = ob_get_clean();
    
    if (!empty($output)) {
        echo "CodeIgniter output captured:<br>";
        echo "<pre>" . htmlspecialchars($output) . "</pre>";
    } else {
        echo "✅ CodeIgniter loaded (no output)<br>";
    }
    
} catch(Exception $e) {
    ob_end_clean();
    echo "❌ CodeIgniter load error: " . $e->getMessage() . "<br>";
    echo "File: " . $e->getFile() . " Line: " . $e->getLine() . "<br>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
} catch(Error $e) {
    ob_end_clean();
    echo "❌ Fatal Error: " . $e->getMessage() . "<br>";
    echo "File: " . $e->getFile() . " Line: " . $e->getLine() . "<br>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

echo "<h3>Diagnostic Complete</h3>";
echo "Check the results above to identify the issue.";
?>
