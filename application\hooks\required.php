<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

function required() {
    try {
        $CI = & get_instance();

        $CI->load->library('Ion_auth');
        $CI->load->library('session');
        $CI->load->library('form_validation');
        $CI->load->library('upload');

        $CI->load->config('paypal');



    $RTR = & load_class('Router');


    if ($RTR->class != "frontend" && $RTR->class != "request" && $RTR->class != "auth") {
        if (!$CI->ion_auth->logged_in()) {
            redirect('auth/login');
        }
    }


    $CI->load->model('settings/settings_model');
    $CI->load->model('ion_auth_model');


    $CI->load->model('hospital/hospital_model');



    if ($RTR->class != "frontend" && $RTR->class != "request" && $RTR->class != "auth") {
        if (!$CI->ion_auth->in_group(array('superadmin'))) {
            if ($CI->ion_auth->in_group(array('admin'))) {
                $user_result = $CI->ion_auth->user();
                if ($user_result && $user_result->num_rows() > 0) {
                    $current_user_id = $user_result->row()->id;
                    $hospital_result = $CI->db->get_where('hospital', array('ion_user_id' => $current_user_id));
                    if ($hospital_result && $hospital_result->num_rows() > 0) {
                        $CI->hospital_id = $hospital_result->row()->id;
                    }
                }

                if (!empty($CI->hospital_id)) {
                    $newdata = array(
                        'hospital_id' => $CI->hospital_id,
                    );
                    $CI->session->set_userdata($newdata);
                }
            } else {
                $user_result = $CI->ion_auth->user();
                if ($user_result && $user_result->num_rows() > 0) {
                    $current_user_id = $user_result->row()->id;
                    $group_result = $CI->db->get_where('users_groups', array('user_id' => $current_user_id));
                    if ($group_result && $group_result->num_rows() > 0) {
                        $group_id = $group_result->row()->group_id;
                        $group_name_result = $CI->db->get_where('groups', array('id' => $group_id));
                        if ($group_name_result && $group_name_result->num_rows() > 0) {
                            $group_name = strtolower($group_name_result->row()->name ?? '');
                            if (!empty($group_name)) {
                                $hospital_result = $CI->db->get_where($group_name, array('ion_user_id' => $current_user_id));
                                if ($hospital_result && $hospital_result->num_rows() > 0) {
                                    $CI->hospital_id = $hospital_result->row()->hospital_id;
                                }
                            }
                        }
                    }
                }
                if (!empty($CI->hospital_id)) {
                    $newdata = array(
                        'hospital_id' => $CI->hospital_id,
                    );
                    $CI->session->set_userdata($newdata);
                }
            }
        }
    }


    if ($RTR->class != "frontend" && $RTR->class != "request" && $RTR->class != "auth") {
        if (!$CI->ion_auth->in_group(array('superadmin'))) {
            $CI->db->where('hospital_id', $CI->hospital_id ?? '');
            $settings_result = $CI->db->get('settings');
            if ($settings_result && $settings_result->num_rows() > 0) {
                $CI->language = $settings_result->row()->language ?? 'english';
                $CI->lang->load('system_syntax', $CI->language);
            }
        } else {
            $CI->db->where('hospital_id', 'superadmin');
            $settings_result = $CI->db->get('settings');
            if ($settings_result && $settings_result->num_rows() > 0) {
                $CI->language = $settings_result->row()->language ?? 'english';
                $CI->lang->load('system_syntax', $CI->language);
            }
        }
    }

    if ($RTR->class == "frontend") {
        $CI->db->where('hospital_id', 'superadmin');
        $settings_result = $CI->db->get('settings');
        if ($settings_result && $settings_result->num_rows() > 0) {
            $CI->language = $settings_result->row()->language ?? 'english';
            $CI->lang->load('system_syntax', $CI->language);
        }
    }

    if ($RTR->class != "frontend" && $RTR->class != "request" && $RTR->class != "auth") {
        if (!$CI->ion_auth->in_group(array('superadmin'))) {
            if ($CI->ion_auth->in_group(array('admin'))) {
                $user_result = $CI->ion_auth->user();
                if ($user_result && $user_result->num_rows() > 0) {
                    $current_user_id = $user_result->row()->id;
                    $hospital_result = $CI->db->get_where('hospital', array('ion_user_id' => $current_user_id));
                    if ($hospital_result && $hospital_result->num_rows() > 0) {
                        $modules = $hospital_result->row()->module ?? '';
                        $CI->modules = explode(',', $modules);
                    } else {
                        $CI->modules = array();
                    }
                } else {
                    $CI->modules = array();
                }
            } else {
                $user_result = $CI->ion_auth->user();
                if ($user_result && $user_result->num_rows() > 0) {
                    $current_user_id = $user_result->row()->id;
                    $group_result = $CI->db->get_where('users_groups', array('user_id' => $current_user_id));
                    if ($group_result && $group_result->num_rows() > 0) {
                        $group_id = $group_result->row()->group_id;
                        $group_name_result = $CI->db->get_where('groups', array('id' => $group_id));
                        if ($group_name_result && $group_name_result->num_rows() > 0) {
                            $group_name = strtolower($group_name_result->row()->name ?? '');
                            if (!empty($group_name)) {
                                $user_group_result = $CI->db->get_where($group_name, array('ion_user_id' => $current_user_id));
                                if ($user_group_result && $user_group_result->num_rows() > 0) {
                                    $hospital_id = $user_group_result->row()->hospital_id;
                                    $hospital_result = $CI->db->get_where('hospital', array('id' => $hospital_id));
                                    if ($hospital_result && $hospital_result->num_rows() > 0) {
                                        $modules = $hospital_result->row()->module ?? '';
                                        $CI->modules = explode(',', $modules);
                                    } else {
                                        $CI->modules = array();
                                    }
                                } else {
                                    $CI->modules = array();
                                }
                            } else {
                                $CI->modules = array();
                            }
                        } else {
                            $CI->modules = array();
                        }
                    } else {
                        $CI->modules = array();
                    }
                } else {
                    $CI->modules = array();
                }
            }
        }
    }


    $common = array('auth', 'frontend', 'settings', 'home', 'profile', 'request');

    if (!in_array($RTR->class, $common)) {
        if (!$CI->ion_auth->in_group(array('superadmin'))) {
            // Ensure modules array exists
            if (!isset($CI->modules) || !is_array($CI->modules)) {
                $CI->modules = array();
            }

            if ($RTR->class != "schedule") {
                if ($RTR->class != "pgateway") {
                    if (!in_array($RTR->class, $CI->modules)) {
                        redirect('home');
                    }
                } elseif (!in_array('finance', $CI->modules)) {
                    redirect('home');
                }
            } elseif (!in_array('appointment', $CI->modules)) {
                redirect('home');
            }
        }
    }

    } catch (Exception $e) {
        // Log the error and continue
        log_message('error', 'Hook error: ' . $e->getMessage());
        // For debugging, you can uncomment the next line
        // echo "Hook error: " . $e->getMessage();
    }
}
