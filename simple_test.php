<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "Simple PHP Test - " . date('Y-m-d H:i:s') . "<br>";
echo "PHP Version: " . phpversion() . "<br>";

// Test if we can access the file directly
echo "This file is accessible!<br>";

// Test database
try {
    $pdo = new PDO('mysql:host=localhost;dbname=database_tables', 'root', '');
    echo "Database connection: OK<br>";
} catch(Exception $e) {
    echo "Database error: " . $e->getMessage() . "<br>";
}

// Test if index.php can be included
echo "<br>Testing index.php inclusion:<br>";
try {
    ob_start();
    include 'index.php';
    $output = ob_get_clean();
    
    if (empty($output)) {
        echo "Index.php included but produced no output<br>";
    } else {
        echo "Index.php output length: " . strlen($output) . " characters<br>";
        echo "First 200 characters:<br>";
        echo "<pre>" . htmlspecialchars(substr($output, 0, 200)) . "</pre>";
    }
} catch(Exception $e) {
    ob_end_clean();
    echo "Error including index.php: " . $e->getMessage() . "<br>";
    echo "File: " . $e->getFile() . " Line: " . $e->getLine() . "<br>";
} catch(Error $e) {
    ob_end_clean();
    echo "Fatal error including index.php: " . $e->getMessage() . "<br>";
    echo "File: " . $e->getFile() . " Line: " . $e->getLine() . "<br>";
}
?>
